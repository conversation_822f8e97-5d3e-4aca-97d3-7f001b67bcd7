@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-700;
  }

  body {
    @apply bg-gray-900 text-white;
    background-image:
      radial-gradient(circle at 20% 80%, rgba(0, 255, 136, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(0, 170, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(170, 0, 255, 0.05) 0%, transparent 50%);
    background-attachment: fixed;
  }
}

@layer components {
  .cyber-card {
    @apply bg-gray-800/50 backdrop-blur-sm border border-green-400/20 rounded-lg p-6 shadow-lg;
    background-image: linear-gradient(135deg, rgba(0, 255, 136, 0.05) 0%, transparent 50%);
    transition: all 0.3s ease;
  }

  .cyber-card:hover {
    @apply border-green-400/40;
    transform: translateY(-2px);
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
  }

  .cyber-button {
    @apply bg-green-400/20 hover:bg-green-400/30 border border-green-400/50 hover:border-green-400 text-green-400 font-semibold px-4 py-2 rounded-lg transition-all duration-300;
  }

  .cyber-button:hover {
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
  }

  .cyber-button-secondary {
    @apply bg-gray-700/50 hover:bg-gray-700/70 border border-green-400/30 hover:border-green-400/50 text-white font-semibold px-4 py-2 rounded-lg transition-all duration-300;
  }

  .cyber-input {
    @apply bg-gray-800/30 border border-green-400/30 focus:border-green-400 focus:ring-1 focus:ring-green-400/50 rounded-lg px-3 py-2 text-white placeholder-gray-400 transition-all duration-300;
  }

  .cyber-nav-item {
    @apply flex items-center space-x-2 px-4 py-3 rounded-lg transition-all duration-300 text-gray-300 hover:text-green-400 hover:bg-green-400/10;
  }

  .cyber-nav-item.active {
    @apply text-green-400 bg-green-400/20 border-l-2 border-green-400;
  }

  .status-indicator {
    @apply w-3 h-3 rounded-full animate-pulse;
  }

  .status-online {
    @apply bg-green-400;
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
  }

  .status-warning {
    @apply bg-yellow-400;
    box-shadow: 0 0 20px rgba(255, 170, 0, 0.3);
  }

  .status-error {
    @apply bg-red-400;
    box-shadow: 0 0 20px rgba(255, 0, 85, 0.3);
  }

  .cyber-grid {
    @apply grid gap-6;
  }

  .cyber-text-glow {
    @apply text-green-400;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
  }

  .cyber-border-glow {
    @apply border border-green-400/50;
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
  }
}
