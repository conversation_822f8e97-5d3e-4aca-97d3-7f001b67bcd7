import React, { useState, useEffect } from 'react';
import {
  Play,
  Pause,
  Square,
  Camera,
  Target,
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  RotateCcw
} from 'lucide-react';

const OperationField = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [currentJob, setCurrentJob] = useState('Idle');
  const [robotStatus, setRobotStatus] = useState('Ready');
  const [visionStatus, setVisionStatus] = useState('Calibrated');

  // Mock real-time data
  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate status updates
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  const handleStart = () => {
    setIsRunning(true);
    setCurrentJob('Processing Part #1234');
    setRobotStatus('Running');
  };

  const handlePause = () => {
    setIsRunning(false);
    setRobotStatus('Paused');
  };

  const handleStop = () => {
    setIsRunning(false);
    setCurrentJob('Idle');
    setRobotStatus('Ready');
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-green-400 cyber-text-glow">
            Operation Field
          </h1>
          <p className="text-gray-400 mt-1">Main Control Interface</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="cyber-card p-3">
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-green-400" />
              <span className="text-green-400 font-mono">
                {new Date().toLocaleTimeString()}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Control Panel */}
      <div className="cyber-grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Robot Control */}
        <div className="cyber-card">
          <h2 className="text-xl font-semibold text-green-400 mb-4 flex items-center">
            <Zap className="w-6 h-6 mr-2" />
            Robot Control
          </h2>
          <div className="space-y-4">
            <div className="flex space-x-2">
              <button
                onClick={handleStart}
                disabled={isRunning}
                className="cyber-button flex-1 flex items-center justify-center space-x-2 disabled:opacity-50"
              >
                <Play className="w-4 h-4" />
                <span>Start</span>
              </button>
              <button
                onClick={handlePause}
                disabled={!isRunning}
                className="cyber-button-secondary flex-1 flex items-center justify-center space-x-2 disabled:opacity-50"
              >
                <Pause className="w-4 h-4" />
                <span>Pause</span>
              </button>
              <button
                onClick={handleStop}
                className="cyber-button-secondary flex-1 flex items-center justify-center space-x-2"
              >
                <Square className="w-4 h-4" />
                <span>Stop</span>
              </button>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-400">Status:</span>
                <span className={`font-semibold ${
                  robotStatus === 'Running' ? 'text-green-400' :
                  robotStatus === 'Paused' ? 'text-yellow-400' : 'text-gray-300'
                }`}>
                  {robotStatus}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Current Job:</span>
                <span className="text-green-400 font-mono text-sm">{currentJob}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Vision System */}
        <div className="cyber-card">
          <h2 className="text-xl font-semibold text-green-400 mb-4 flex items-center">
            <Camera className="w-6 h-6 mr-2" />
            Vision System
          </h2>
          <div className="space-y-4">
            <div className="bg-gray-900/50 rounded-lg p-4 border border-green-400/20">
              <div className="aspect-video bg-gray-800/30 rounded-lg flex items-center justify-center border border-green-400/30">
                <div className="text-center">
                  <Camera className="w-12 h-12 text-green-400/50 mx-auto mb-2" />
                  <p className="text-gray-400 text-sm">Camera Feed</p>
                  <p className="text-green-400 text-xs">1920x1080 @ 30fps</p>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-400">Status:</span>
                <span className="text-green-400 font-semibold">{visionStatus}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Last Detection:</span>
                <span className="text-green-400 font-mono text-sm">2.3s ago</span>
              </div>
            </div>
          </div>
        </div>

        {/* System Status */}
        <div className="cyber-card">
          <h2 className="text-xl font-semibold text-green-400 mb-4 flex items-center">
            <Target className="w-6 h-6 mr-2" />
            System Status
          </h2>
          <div className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-green-400/10 rounded-lg border border-green-400/30">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>Robot Connected</span>
                </div>
                <div className="status-indicator status-online"></div>
              </div>
              <div className="flex items-center justify-between p-3 bg-green-400/10 rounded-lg border border-green-400/30">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>Vision Calibrated</span>
                </div>
                <div className="status-indicator status-online"></div>
              </div>
              <div className="flex items-center justify-between p-3 bg-yellow-400/10 rounded-lg border border-yellow-400/30">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="w-5 h-5 text-yellow-400" />
                  <span>Tool Wear: 85%</span>
                </div>
                <div className="status-indicator status-warning"></div>
              </div>
            </div>
            <button className="cyber-button w-full flex items-center justify-center space-x-2">
              <RotateCcw className="w-4 h-4" />
              <span>Refresh Status</span>
            </button>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="cyber-card">
        <h2 className="text-xl font-semibold text-cyber-green mb-4">Recent Activity</h2>
        <div className="space-y-2">
          {[
            { time: '14:32:15', message: 'Part #1234 processed successfully', type: 'success' },
            { time: '14:31:42', message: 'Vision calibration verified', type: 'info' },
            { time: '14:30:18', message: 'Robot position updated', type: 'info' },
            { time: '14:29:55', message: 'Tool wear warning threshold reached', type: 'warning' },
          ].map((activity, index) => (
            <div key={index} className="flex items-center space-x-4 p-3 bg-cyber-gray/30 rounded-lg">
              <span className="text-cyber-green font-mono text-sm">{activity.time}</span>
              <span className="flex-1 text-gray-300">{activity.message}</span>
              <div className={`status-indicator ${
                activity.type === 'success' ? 'status-online' :
                activity.type === 'warning' ? 'status-warning' : 'status-online'
              }`}></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default OperationField;
