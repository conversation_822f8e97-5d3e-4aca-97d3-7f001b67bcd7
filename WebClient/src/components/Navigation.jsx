import React from 'react';
import { NavLink } from 'react-router-dom';
import {
  Monitor,
  Settings,
  Activity,
  Cpu,
  Wifi,
  Battery
} from 'lucide-react';

const Navigation = () => {
  const navItems = [
    {
      path: '/',
      name: 'Operation Field',
      icon: Monitor,
      description: 'Main Control Interface'
    },
    {
      path: '/techin',
      name: 'Tech In',
      icon: Settings,
      description: 'Technician Interface'
    },
    {
      path: '/service',
      name: 'Service',
      icon: Activity,
      description: 'System Monitoring'
    }
  ];

  return (
    <div className="w-64 h-screen bg-gray-900/80 backdrop-blur-sm border-r border-green-400/20 flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-green-400/20">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-green-400/20 rounded-lg flex items-center justify-center border border-green-400/50">
            <Cpu className="w-6 h-6 text-green-400" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-green-400 cyber-text-glow">
              TendoVision
            </h1>
            <p className="text-xs text-gray-400">Robot Vision System</p>
          </div>
        </div>
      </div>

      {/* Navigation Items */}
      <nav className="flex-1 p-4 space-y-2">
        {navItems.map((item) => {
          const IconComponent = item.icon;
          return (
            <NavLink
              key={item.path}
              to={item.path}
              className={({ isActive }) =>
                `cyber-nav-item ${isActive ? 'active' : ''}`
              }
            >
              <IconComponent className="w-5 h-5" />
              <div className="flex-1">
                <div className="font-semibold">{item.name}</div>
                <div className="text-xs text-gray-500">{item.description}</div>
              </div>
            </NavLink>
          );
        })}
      </nav>

      {/* System Status */}
      <div className="p-4 border-t border-green-400/20">
        <div className="cyber-card p-4">
          <h3 className="text-sm font-semibold text-green-400 mb-3">System Status</h3>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Wifi className="w-4 h-4 text-green-400" />
                <span className="text-xs">Connection</span>
              </div>
              <div className="status-indicator status-online"></div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Cpu className="w-4 h-4 text-green-400" />
                <span className="text-xs">Robot</span>
              </div>
              <div className="status-indicator status-online"></div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Battery className="w-4 h-4 text-green-400" />
                <span className="text-xs">Vision</span>
              </div>
              <div className="status-indicator status-online"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Navigation;
