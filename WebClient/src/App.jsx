import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navigation from './components/Navigation';
import OperationField from './pages/OperationField';
import TechIn from './pages/TechIn';
import Service from './pages/Service';

function App() {
  return (
    <Router>
      <div className="flex h-screen bg-gray-900 overflow-hidden">
        <Navigation />
        <main className="flex-1 overflow-auto">
          <Routes>
            <Route path="/" element={<OperationField />} />
            <Route path="/techin" element={<TechIn />} />
            <Route path="/service" element={<Service />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
